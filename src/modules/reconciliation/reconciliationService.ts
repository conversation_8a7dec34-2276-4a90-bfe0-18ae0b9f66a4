import { prisma } from "@/prisma/prisma";
import { Prisma, ReconciliationStatus } from "@/prisma/generated";
import { ResultAsync } from "neverthrow";
import { startOfDay } from "date-fns";
import { textUtils } from "@/modules/core/utils/textUtils";
import { cohere } from "../ai/embeddings/cohere";
import { measureTime } from "@/utils";
import { zip } from "remeda";
import pLimit from "p-limit";

export interface ReconciliationMatch {
  invoiceId: string;
  transferId: string;
  confidenceScore: number;
  matchReason: string;
  detailedScore?: DetailedMatchScore;
}

export interface DetailedMatchScore {
  totalScore: number;
  maxPossibleScore: number;
  breakdown: {
    amount: {
      score: number;
      maxScore: number;
      reason: string;
      details: string;
    };
    date: {
      score: number;
      maxScore: number;
      reason: string;
      details: string;
    };
    vendor: {
      score: number;
      maxScore: number;
      reason: string;
      details: string;
    };
  };
  overallReason: string;
}

export interface ReconciliationConfig {
  // Maximum days difference between invoice date and transfer date
  maxDaysDifference: number;
  // Minimum confidence score to auto-match
  minAutoMatchConfidence: number;
  // Amount tolerance percentage (e.g., 0.01 for 1%)
  amountToleranceFraction: number;
}

export const DEFAULT_CONFIG: ReconciliationConfig = {
  maxDaysDifference: 30,
  minAutoMatchConfidence: 90,
  amountToleranceFraction: 0.05, // 2%
};

export const globalTransferWhereClause: Prisma.TransferWhereInput = {
  transaction: {
    account: {
      type: { not: "WALLET" },
    },
  },
};

/**
 * Calculate detailed confidence score breakdown for a potential match between invoice and transfer
 */
export async function calculateConfidenceScore(
  invoice: {
    date: Date;
    amountGross: Prisma.Decimal;
    currencyCode: string;
    vendorName?: string | null;
  },
  transfer: {
    amount: Prisma.Decimal;
    currencyCode: string;
    counterparty?: string | null;
    description?: string | null;
  },
  transaction: { executedAt: Date },
  config: ReconciliationConfig
): Promise<DetailedMatchScore> {
  const breakdown = {
    amount: { score: 0, maxScore: 45, reason: "", details: "" },
    vendor: { score: 0, maxScore: 45, reason: "", details: "" },
    date: { score: 0, maxScore: 30, reason: "", details: "" },
  };

  const invoiceAmount = Math.abs(Number(invoice.amountGross));
  const transferAmount = Math.abs(Number(transfer.amount));
  const amountDiff = Math.abs(invoiceAmount - transferAmount);

  const normalizedInvoiceDate = startOfDay(invoice.date);
  const normalizedTransferDate = startOfDay(transaction.executedAt);

  // Check if invoice date is after transfer date (invalid)
  if (normalizedInvoiceDate.getTime() > normalizedTransferDate.getTime()) {
    return {
      totalScore: 0,
      maxPossibleScore: 110,
      breakdown,
      overallReason: "invoice_date_after_transfer_date",
    };
  }

  // Amount matching (50 points max)
  if (amountDiff === 0) {
    breakdown.amount.reason = "exact_amount";
    breakdown.amount.details = "Perfect amount match";
  } else if (amountDiff <= config.amountToleranceFraction * invoiceAmount) {
    breakdown.amount.reason = "amount_within_tolerance";
    breakdown.amount.details = `Within ${(config.amountToleranceFraction * 100).toFixed(1)}% tolerance`;
  } else if (amountDiff <= invoiceAmount * 0.04) {
    breakdown.amount.reason = "amount_close";
    breakdown.amount.details = "Within 3% difference";
  } else {
    breakdown.amount.reason = "amount_not_close";
    breakdown.amount.details = `Difference: ${amountDiff.toFixed(2)}`;
    return {
      totalScore: 0,
      maxPossibleScore: 110,
      breakdown,
      overallReason: "amount_not_close",
    };
  }

  const amountScoreFraction = invoiceAmount > transferAmount ? transferAmount / invoiceAmount : invoiceAmount / transferAmount;
  const amountScore = breakdown.amount.maxScore * amountScoreFraction;
  breakdown.amount.score = amountScore;

  // Date proximity (30 points max)
  const daysDiff = Math.abs((normalizedInvoiceDate.getTime() - normalizedTransferDate.getTime()) / (1000 * 60 * 60 * 24));

  if (daysDiff <= 1) {
    breakdown.date.score = breakdown.date.maxScore;
    breakdown.date.reason = "same_date";
    breakdown.date.details = daysDiff === 0 ? "Same day" : "Next day";
  } else {
    const remainingRange = config.maxDaysDifference;
    const scale = (remainingRange - daysDiff) / remainingRange;
    breakdown.date.score = breakdown.date.maxScore * Math.max(scale, 0);
    breakdown.date.reason = "within_max_days";
    breakdown.date.details = `${Math.round(daysDiff)} days difference`;
  }
  if (daysDiff > 60) {
    breakdown.date.reason = "days_diff_over_60";
    breakdown.date.details = "Over 60 days difference";
    breakdown.date.score = 0;
    return {
      totalScore: 0,
      maxPossibleScore: 110,
      breakdown,
      overallReason: "days_diff_over_60",
    };
  }

  // Vendor name similarity (30 points max)
  if (invoice.vendorName) {
    let transactionVendorText = transfer.description ?? "";

    if (transfer.description) {
      const cardDescription = parseCardDescription(transfer.description);

      if (cardDescription) {
        transactionVendorText = cardDescription.companyName;
      }
    }

    // const sim = textUtils.similarity(invoice.vendorName, transactionVendorText);
    // const sim = textUtils.fuzzyMatch(invoice.vendorName, transactionVendorText);

    const sim = await measureTime("vendorSimilarity", async () =>
      !transactionVendorText || !invoice.vendorName ? 0.2 : await cohere.embedSimilarity(invoice.vendorName, transactionVendorText)
    );

    breakdown.vendor.score = breakdown.vendor.maxScore * sim;

    if (sim >= 0.9) {
      breakdown.vendor.reason = "vendor_exact";
      breakdown.vendor.details = "High similarity match";
    } else if (sim >= 0.5) {
      breakdown.vendor.reason = "vendor_fuzzy";
      breakdown.vendor.details = `${(sim * 100).toFixed(0)}% similarity`;
    } else {
      breakdown.vendor.reason = "vendor_no_match";
      breakdown.vendor.details = `${(sim * 100).toFixed(0)}% similarity`;
    }
  } else {
    breakdown.vendor.reason = "no_vendor_name";
    breakdown.vendor.details = "No vendor name to compare";
  }

  const totalScore = breakdown.amount.score + breakdown.date.score + breakdown.vendor.score;
  const reasons = [breakdown.amount.reason, breakdown.date.reason, breakdown.vendor.reason].filter(Boolean);

  return {
    totalScore: Math.min(totalScore, 100),
    maxPossibleScore: 100,
    breakdown,
    overallReason: reasons.join(", "),
  };
}

/**
 * Find potential matches for unmatched invoices and transfers
 */
export async function findPotentialMatches(
  config: ReconciliationConfig & {
    invoiceId?: string;
  } = DEFAULT_CONFIG
) {
  return ResultAsync.fromPromise(
    (async () => {
      // Get unmatched invoices
      const unmatchedInvoices = await prisma.invoice.findMany({
        where: {
          reconciliations: {
            none: {},
          },
          ...(config.invoiceId ? { id: config.invoiceId } : {}),
        },
        include: {
          vendor: {
            select: { name: true },
          },
        },
      });

      // Get unmatched transfers with their transaction dates
      const unmatchedTransfers = await prisma.transfer.findMany({
        where: {
          // reconciliations: {
          //   none: {},
          // },
          ...globalTransferWhereClause,
        },
        include: {
          transaction: {
            select: {
              executedAt: true,
              account: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
      });

      const invoicesTransferMerged = zip(unmatchedInvoices, unmatchedTransfers);

      const concurrency = pLimit(20);

      const potentialMatches = await Promise.all(
        invoicesTransferMerged.map(([invoice, transfer]) => {
          return concurrency(async () => {
            const detailedScore = await calculateConfidenceScore({ ...invoice, vendorName: invoice.vendor?.name }, transfer, transfer.transaction, config);

            if (detailedScore.totalScore > 0) {
              const debug = 1;
            }

            if (detailedScore.totalScore < 60) {
              return null;
            }

            return {
              invoiceId: invoice.id,
              transferId: transfer.id,
              confidenceScore: detailedScore.totalScore,
              matchReason: detailedScore.overallReason,
              detailedScore,
              invoice,
              transfer,
            };
          });
        })
      ).then((matches) => matches.filter((m) => m !== null).map((m) => m!));

      // Find matches for each invoice
      // for (const invoice of unmatchedInvoices) {
      //   for (const transfer of unmatchedTransfers) {
      //     const detailedScore = await calculateConfidenceScore({ ...invoice, vendorName: invoice.vendor?.name }, transfer, transfer.transaction, config);

      //     // Only consider matches above a minimum threshold
      //     if (detailedScore.totalScore >= 60) {
      //       potentialMatches.push({
      //         invoiceId: invoice.id,
      //         transferId: transfer.id,
      //         confidenceScore: detailedScore.totalScore,
      //         matchReason: detailedScore.overallReason,
      //         detailedScore,
      //         invoice,
      //         transfer,
      //       });
      //     }
      //   }
      // }

      // Sort by confidence score descending
      return potentialMatches.sort((a, b) => b.confidenceScore - a.confidenceScore);
    })(),
    (err) => ({
      type: "error" as const,
      message: "Failed to find potential matches",
      err,
    })
  );
}

/**
 * Find potential invoice matches for transfers with custom filtering
 */
export async function findPotentialMatchesFromTransfers(transferWhereClause: Prisma.TransferWhereInput, config: ReconciliationConfig = DEFAULT_CONFIG) {
  return ResultAsync.fromPromise(
    (async () => {
      // Get unmatched invoices
      const unmatchedInvoices = await prisma.invoice.findMany({
        where: {
          reconciliations: {
            none: {},
          },
        },
        include: {
          vendor: {
            select: { name: true },
          },
        },
      });

      // Get filtered transfers with their transaction dates
      const filteredTransfers = await prisma.transfer.findMany({
        where: {
          reconciliations: {
            none: {},
          },
          ...globalTransferWhereClause,
          ...transferWhereClause,
        },
        include: {
          transaction: {
            select: {
              executedAt: true,
              account: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
      });

      const potentialMatches: (ReconciliationMatch & {
        invoice: (typeof unmatchedInvoices)[0];
        transfer: (typeof filteredTransfers)[0];
      })[] = [];

      // Find matches for each transfer
      for (const transfer of filteredTransfers) {
        for (const invoice of unmatchedInvoices) {
          const detailedScore = await calculateConfidenceScore({ ...invoice, vendorName: invoice.vendor?.name }, transfer, transfer.transaction, config);

          // Only consider matches above a minimum threshold
          if (detailedScore.totalScore >= 60) {
            potentialMatches.push({
              invoiceId: invoice.id,
              transferId: transfer.id,
              confidenceScore: detailedScore.totalScore,
              matchReason: detailedScore.overallReason,
              detailedScore,
              invoice,
              transfer,
            });
          }
        }
      }

      // Sort by confidence score descending
      return potentialMatches.sort((a, b) => b.confidenceScore - a.confidenceScore);
    })(),
    (err) => ({
      type: "error" as const,
      message: "Failed to find potential matches from transfers",
      err,
    })
  );
}

/**
 * Store reconciliation matches in the database
 */
export async function storeReconciliationMatches(
  matches: ReconciliationMatch[],
  minAutoMatchConfidence: number = DEFAULT_CONFIG.minAutoMatchConfidence
): Promise<ResultAsync<number, { type: "error"; message: string; err: unknown }>> {
  return ResultAsync.fromPromise(
    (async () => {
      const autoMatches = matches.filter((match) => match.confidenceScore >= minAutoMatchConfidence);

      if (autoMatches.length === 0) {
        return 0;
      }

      // Create reconciliation records for auto matches
      const reconciliations = await prisma.reconciliation.createMany({
        data: autoMatches.map((match) => ({
          invoiceId: match.invoiceId,
          transferId: match.transferId,
          status: ReconciliationStatus.AUTO_MATCHED,
          confidenceScore: match.confidenceScore,
          matchReason: match.matchReason,
          isManualOverride: false,
        })),
        skipDuplicates: true,
      });

      return reconciliations.count;
    })(),
    (err) => ({
      type: "error" as const,
      message: "Failed to store reconciliation matches",
      err,
    })
  );
}

/**
 * Automatically match invoices and transfers based on confidence score
 */
export async function autoReconcile(
  config: ReconciliationConfig = DEFAULT_CONFIG
): Promise<ResultAsync<number, { type: "error"; message: string; err: unknown }>> {
  return ResultAsync.fromPromise(
    (async () => {
      const potentialMatchesResult = await findPotentialMatches(config);
      if (potentialMatchesResult.isErr()) {
        throw potentialMatchesResult.error;
      }

      const potentialMatches = potentialMatchesResult.value;
      return (await storeReconciliationMatches(potentialMatches, config.minAutoMatchConfidence)).unwrapOr(0);
    })(),
    (err) => ({
      type: "error" as const,
      message: "Failed to auto reconcile",
      err,
    })
  );
}

/**
 * Manually match an invoice with a transfer
 */
export async function manualMatch(invoiceId: string, transferId: string): Promise<ResultAsync<void, { type: "error"; message: string; err: unknown }>> {
  return ResultAsync.fromPromise(
    (async () => {
      await prisma.reconciliation.upsert({
        where: {
          invoiceId,
        },
        create: {
          invoiceId,
          transferId,
          status: ReconciliationStatus.MANUALLY_MATCHED,
          isManualOverride: true,
          confidenceScore: 100,
          matchReason: "manual_match",
        },
        update: {
          status: ReconciliationStatus.MANUALLY_MATCHED,
          isManualOverride: true,
        },
      });
    })(),
    (err) => ({
      type: "error" as const,
      message: "Failed to manually match",
      err,
    })
  );
}

/**
 * Manually unmatch an invoice from a transfer
 */
export async function manualUnmatch(invoiceId: string, transferId: string): Promise<ResultAsync<void, { type: "error"; message: string; err: unknown }>> {
  return ResultAsync.fromPromise(
    (async () => {
      await prisma.reconciliation.upsert({
        where: {
          invoiceId,
        },
        create: {
          invoiceId,
          transferId,
          status: ReconciliationStatus.MANUALLY_UNMATCHED,
          isManualOverride: true,
          confidenceScore: 0,
          matchReason: "manual_unmatch",
        },
        update: {
          status: ReconciliationStatus.MANUALLY_UNMATCHED,
          isManualOverride: true,
        },
      });
    })(),
    (err) => ({
      type: "error" as const,
      message: "Failed to manually unmatch",
      err,
    })
  );
}

/**
 * Auto-reconcile for a specific invoice (when a new invoice is created)
 */
export async function autoReconcileForInvoice(
  invoiceId: string,
  config: ReconciliationConfig = DEFAULT_CONFIG
): Promise<ResultAsync<number, { type: "error"; message: string; err: unknown }>> {
  return ResultAsync.fromPromise(
    (async () => {
      // Get the invoice
      const invoice = await prisma.invoice.findUnique({
        where: { id: invoiceId },
        select: {
          id: true,
          date: true,
          amountGross: true,
          currencyCode: true,
          vendor: {
            select: { name: true },
          },
        },
      });

      if (!invoice) {
        throw new Error("Invoice not found");
      }

      // Get unmatched transfers
      const unmatchedTransfers = await prisma.transfer.findMany({
        where: {
          reconciliations: {
            none: {},
          },
          ...globalTransferWhereClause,
        },
        select: {
          id: true,
          amount: true,
          currencyCode: true,
          counterparty: true,
          description: true,
          transaction: {
            select: {
              executedAt: true,
            },
          },
        },
      });

      const potentialMatches: ReconciliationMatch[] = [];

      // Find matches for this invoice
      for (const transfer of unmatchedTransfers) {
        const detailedScore = await calculateConfidenceScore({ ...invoice, vendorName: invoice.vendor?.name }, transfer, transfer.transaction, config);

        // Only consider matches above a minimum threshold
        if (detailedScore.totalScore >= config.minAutoMatchConfidence) {
          potentialMatches.push({
            invoiceId: invoice.id,
            transferId: transfer.id,
            confidenceScore: detailedScore.totalScore,
            matchReason: detailedScore.overallReason,
          });
        }
      }

      // Store reconciliation matches
      const storeResult = await storeReconciliationMatches(potentialMatches, config.minAutoMatchConfidence);
      if (storeResult.isErr()) {
        throw storeResult.error;
      }

      return storeResult.value;
    })(),
    (err) => ({
      type: "error" as const,
      message: "Failed to auto reconcile for invoice",
      err,
    })
  );
}

/**
 * Auto-reconcile for a specific transfer (when a new transfer is created)
 */
export async function autoReconcileForTransfer(
  transferId: string,
  config: ReconciliationConfig = DEFAULT_CONFIG
): Promise<ResultAsync<number, { type: "error"; message: string; err: unknown }>> {
  return ResultAsync.fromPromise(
    (async () => {
      // Get the transfer with transaction
      const transfer = await prisma.transfer.findUnique({
        where: {
          id: transferId,
          transaction: {
            account: {
              type: { not: "WALLET" },
            },
          },
        },
        select: {
          id: true,
          amount: true,
          currencyCode: true,
          counterparty: true,
          description: true,
          transaction: {
            select: {
              executedAt: true,
            },
          },
        },
      });

      if (!transfer) {
        throw new Error("Transfer not found");
      }

      // Get unmatched invoices
      const unmatchedInvoices = await prisma.invoice.findMany({
        where: {
          reconciliations: {
            none: {},
          },
        },
        select: {
          id: true,
          date: true,
          amountGross: true,
          currencyCode: true,
          vendor: {
            select: { name: true },
          },
        },
      });

      const potentialMatches: ReconciliationMatch[] = [];

      // Find matches for this transfer
      for (const invoice of unmatchedInvoices) {
        const detailedScore = await calculateConfidenceScore({ ...invoice, vendorName: invoice.vendor?.name }, transfer, transfer.transaction, config);

        // Only consider matches above a minimum threshold
        if (detailedScore.totalScore >= config.minAutoMatchConfidence) {
          potentialMatches.push({
            invoiceId: invoice.id,
            transferId: transfer.id,
            confidenceScore: detailedScore.totalScore,
            matchReason: detailedScore.overallReason,
          });
        }
      }

      // Store reconciliation matches
      const storeResult = await storeReconciliationMatches(potentialMatches, config.minAutoMatchConfidence);
      if (storeResult.isErr()) {
        throw storeResult.error;
      }

      return storeResult.value;
    })(),
    (err) => ({
      type: "error" as const,
      message: "Failed to auto reconcile for transfer",
      err,
    })
  );
}

/**
 * Get reconciliation statistics
 */
export async function getReconciliationStats(): Promise<
  ResultAsync<
    {
      totalInvoices: number;
      totalTransfers: number;
      matchedInvoices: number;
      matchedTransfers: number;
      autoMatched: number;
      manuallyMatched: number;
      potentialMatches: number;
    },
    { type: "error"; message: string; err: unknown }
  >
> {
  return ResultAsync.fromPromise(
    (async () => {
      const [totalInvoices, totalTransfers, matchedInvoices, matchedTransfers, autoMatched, manuallyMatched, potentialMatchesResult] = await Promise.all([
        prisma.invoice.count(),
        prisma.transfer.count(),
        prisma.invoice.count({
          where: {
            reconciliations: {
              some: {
                status: {
                  in: [ReconciliationStatus.AUTO_MATCHED, ReconciliationStatus.MANUALLY_MATCHED],
                },
              },
            },
          },
        }),
        prisma.transfer.count({
          where: {
            reconciliations: {
              some: {
                status: {
                  in: [ReconciliationStatus.AUTO_MATCHED, ReconciliationStatus.MANUALLY_MATCHED],
                },
              },
            },
          },
        }),
        prisma.reconciliation.count({
          where: { status: ReconciliationStatus.AUTO_MATCHED },
        }),
        prisma.reconciliation.count({
          where: { status: ReconciliationStatus.MANUALLY_MATCHED },
        }),
        findPotentialMatches(),
      ]);

      const potentialMatches = potentialMatchesResult.isOk() ? potentialMatchesResult.value.length : 0;

      return {
        totalInvoices,
        totalTransfers,
        matchedInvoices,
        matchedTransfers,
        autoMatched,
        manuallyMatched,
        potentialMatches,
      };
    })(),
    (err) => ({
      type: "error" as const,
      message: "Failed to get reconciliation stats",
      err,
    })
  );
}

export function parseCardDescription(description: string): { companyName: string; phoneNumber: string; countryCode: string } | null {
  const match = description.trim().match(/^(.*)\s\+([0-9]+)\s*([A-Z]{2})$/i);

  if (!match) return null;

  const [, rawCompany, phone, country] = match;
  const companyName = rawCompany.replace(/\*/g, "").trim();
  return {
    companyName,
    phoneNumber: `+${phone}`,
    countryCode: country.toUpperCase(),
  };
}
